plugins {
    id 'com.android.application'
}

android {
    namespace "com.ks.app.service.statussaver"
    compileSdk 34 // ✅ REVERTED: API 35 causes build failures

    defaultConfig {
        applicationId "com.ks.app.service.statussaver"
        minSdk 21
        targetSdk 34 // ✅ STABLE: Proven to work perfectly
        versionCode 21
        versionName "3.3"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables.useSupportLibrary = true
        multiDexEnabled true // ✅ Required for large app with many dependencies
    }

    signingConfigs {
        release {
            storeFile file(RELEASE_STORE_FILE)  // 🔒 Securely reference in gradle.properties
            storePassword RELEASE_STORE_PASSWORD
            keyAlias RELEASE_KEY_ALIAS
            keyPassword RELEASE_KEY_PASSWORD
        }
    }

    buildTypes {
        debug {
            applicationIdSuffix ".debug"
            debuggable true
            minifyEnabled false
            shrinkResources false
        }
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            debuggable false

            // Proguard rules for optimization and obfuscation
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            zipAlignEnabled true
        }
    }

    // ✅ ABI splits for smaller APKs, with universal APK for compatibility
    splits {
        abi {
            enable true
            reset()
            include 'armeabi-v7a', 'arm64-v8a'
            universalApk true  // ✅ Required to show Install button for more devices when using APK
        }
    }

    packagingOptions {
        resources {
            excludes += [
                'META-INF/DEPENDENCIES',
                'META-INF/LICENSE',
                'META-INF/LICENSE.txt',
                'META-INF/NOTICE',
                'META-INF/NOTICE.txt',
                'META-INF/ASL2.0'
            ]
        }
    }

    lint {
        checkReleaseBuilds false
        abortOnError false
        disable 'InvalidPackage'
    }

    // Exclude unnecessary files to reduce APK size
    packagingOptions {
        resources {
            excludes += [
                'META-INF/DEPENDENCIES',
                'META-INF/LICENSE',
                'META-INF/LICENSE.txt',
                'META-INF/NOTICE',
                'META-INF/NOTICE.txt',
                'META-INF/ASL2.0'
            ]
        }
    }

    // Lint options for faster builds and to avoid failing on lint errors
    lint {
        checkReleaseBuilds false
        abortOnError false
        disable 'InvalidPackage'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    buildFeatures {
        buildConfig true
        resValues true
    }
}

dependencies {
    // AndroidX Core
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.core:core:1.12.0'


    // UI Components
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // ExoPlayer Media3
    implementation 'androidx.media3:media3-exoplayer:1.4.1'
    implementation 'androidx.media3:media3-ui:1.4.1'
    implementation 'androidx.media3:media3-common:1.4.1'

    // Glide & PhotoView
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.16.0'
    implementation 'com.github.chrisbanes:PhotoView:2.3.0'

    // Shimmer Effect
    implementation 'com.facebook.shimmer:shimmer:0.5.0'



    // Google Ads & In-App Update
    implementation 'com.google.android.gms:play-services-ads:22.6.0'
    implementation 'com.google.android.play:app-update:2.1.0'





    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'


}
